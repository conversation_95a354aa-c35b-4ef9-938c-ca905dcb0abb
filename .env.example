# Notion MCP Server Configuration
# Copy this file to .env and fill in your actual values

# Notion Integration Token (Required)
# Get this from: https://www.notion.so/profile/integrations
NOTION_TOKEN=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

# Alternative configuration using headers (Advanced users only)
# OPENAPI_MCP_HEADERS={"Authorization": "Bearer ntn_your_token_here", "Notion-Version": "2022-06-28"}

# HTTP Transport Configuration (Optional)
# Only needed if using HTTP transport mode
AUTH_TOKEN=your_secure_auth_token_here
HTTP_PORT=3000

# Traefik Configuration (Optional)
# Only needed if using Traefik reverse proxy
TRAEFIK_DOMAIN=notion-mcp.yourdomain.com
TRAEFIK_NETWORK=traefik

# Development/Debug Settings
DEBUG=false
LOG_LEVEL=info
