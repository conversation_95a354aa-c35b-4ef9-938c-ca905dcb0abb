{
  "mcpServers": {
    "notionApi": {
      "command": "npx",
      "args": ["-y", "@notionhq/notion-mcp-server"],
      "env": {
        "NOTION_TOKEN": "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"
      }
    }
  }
}

// For Cursor, this configuration should be placed in:
// ~/.cursor/mcp.json

// Alternative Docker configuration for Cursor:
/*
{
  "mcpServers": {
    "notionApi": {
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "-e", "NOTION_TOKEN",
        "mcp/notion"
      ],
      "env": {
        "NOTION_TOKEN": "ntn_your_integration_token_here"
      }
    }
  }
}
*/
