{
  "mcpServers": {
    "notionApi": {
      "command": "npx",
      "args": ["-y", "@notionhq/notion-mcp-server"],
      "env": {
        "NOTION_TOKEN": "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"
      }
    }
  }
}

// For Cursor, this configuration should be placed in:
// ~/.cursor/mcp.json

// Alternative Docker configuration for Cursor:
/*
{
  "mcpServers": {
    "notionApi": {
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "-e", "NOTION_TOKEN",
        "mcp/notion"
      ],
      "env": {
        "NOTION_TOKEN": "ntn_your_integration_token_here"
      }
    }
  }
}
*/
