{
  "context_servers": {
    "notion-mcp-server": {
      "command": {
        "path": "npx",
        "args": ["-y", "@notionhq/notion-mcp-server"],
        "env": {
          "NOTION_TOKEN": "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"
        }
      },
      "settings": {}
    }
  }
}

// For Zed, this configuration should be added to your settings.json file

// Alternative configuration using headers:
/*
{
  "context_servers": {
    "notion-mcp-server": {
      "command": {
        "path": "npx",
        "args": ["-y", "@notionhq/notion-mcp-server"],
        "env": {
          "OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer ntn_your_token_here\", \"Notion-Version\": \"2022-06-28\"}"
        }
      },
      "settings": {}
    }
  }
}
*/
