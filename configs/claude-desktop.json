{
  "mcpServers": {
    "notionApi": {
      "command": "npx",
      "args": ["-y", "@notionhq/notion-mcp-server"],
      "env": {
        "NOTION_TOKEN": "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"
      }
    }
  }
}

// Alternative configurations:

// Using Docker:
/*
{
  "mcpServers": {
    "notionApi": {
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "-e", "NOTION_TOKEN",
        "mcp/notion"
      ],
      "env": {
        "NOTION_TOKEN": "ntn_your_integration_token_here"
      }
    }
  }
}
*/

// Using HTTP transport:
/*
{
  "mcpServers": {
    "notionApi": {
      "command": "npx",
      "args": ["-y", "@notionhq/notion-mcp-server", "--transport", "http", "--port", "3000"],
      "env": {
        "NOTION_TOKEN": "ntn_your_integration_token_here",
        "AUTH_TOKEN": "your_secure_auth_token_here"
      }
    }
  }
}
*/

// Using advanced headers configuration:
/*
{
  "mcpServers": {
    "notionApi": {
      "command": "npx",
      "args": ["-y", "@notionhq/notion-mcp-server"],
      "env": {
        "OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer ntn_your_token_here\", \"Notion-Version\": \"2022-06-28\"}"
      }
    }
  }
}
*/
