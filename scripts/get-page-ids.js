#!/usr/bin/env node

/**
 * 获取 Notion 页面 ID 的脚本
 * 用于 Trae MCP 配置
 */

require('dotenv').config();
const axios = require('axios');

const NOTION_TOKEN = process.env.NOTION_TOKEN;
const NOTION_API_URL = 'https://api.notion.com/v1';

async function getPageIds() {
    console.log('🔍 获取 Notion 页面 ID');
    console.log('====================');

    if (!NOTION_TOKEN) {
        console.error('❌ 未找到 NOTION_TOKEN 环境变量');
        console.log('请确保 .env 文件中设置了正确的令牌');
        process.exit(1);
    }

    try {
        // 搜索所有可访问的页面和数据库
        console.log('🔍 搜索可访问的页面和数据库...');
        
        const searchResponse = await axios.post(`${NOTION_API_URL}/search`, {
            query: '',
            page_size: 20,
            filter: {
                value: "page",
                property: "object"
            }
        }, {
            headers: {
                'Authorization': `Bearer ${NOTION_TOKEN}`,
                'Notion-Version': '2022-06-28',
                'Content-Type': 'application/json'
            }
        });

        const pages = searchResponse.data.results;
        
        if (pages.length === 0) {
            console.log('⚠️  未找到可访问的页面');
            console.log('');
            console.log('请确保：');
            console.log('1. 已将页面连接到您的集成');
            console.log('2. 集成具有读取权限');
            console.log('');
            console.log('连接页面的方法：');
            console.log('- 打开 Notion 页面');
            console.log('- 点击 "..." → "连接到集成"');
            console.log('- 选择您的 "ai ide" 集成');
            return;
        }

        console.log(`✅ 找到 ${pages.length} 个可访问的页面：`);
        console.log('');

        pages.forEach((page, index) => {
            const title = page.properties?.title?.title?.[0]?.plain_text || 
                         page.properties?.Name?.title?.[0]?.plain_text ||
                         '无标题页面';
            
            console.log(`${index + 1}. 📄 ${title}`);
            console.log(`   页面 ID: ${page.id}`);
            console.log(`   URL: https://www.notion.so/${page.id.replace(/-/g, '')}`);
            console.log('');
        });

        // 生成 Trae 配置
        if (pages.length > 0) {
            const firstPageId = pages[0].id;
            console.log('🔧 为 Trae 生成的 MCP 配置：');
            console.log('================================');
            
            const traeConfig = {
                "mcpServers": {
                    "Notion": {
                        "command": "npx",
                        "args": [
                            "-y",
                            "notion-mcp-server"
                        ],
                        "env": {
                            "NOTION_PAGE_ID": firstPageId,
                            "NOTION_TOKEN": NOTION_TOKEN
                        }
                    }
                }
            };

            console.log(JSON.stringify(traeConfig, null, 2));
            console.log('');
            console.log('💡 提示：');
            console.log('- 复制上面的 JSON 配置到 Trae 的 MCP 设置中');
            console.log('- 如果需要访问其他页面，可以更改 NOTION_PAGE_ID');
            console.log('- 页面 ID 可以从上面的列表中选择');
        }

    } catch (error) {
        console.error('❌ 获取页面 ID 失败');
        
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.message || '未知错误';
            
            console.error(`HTTP ${status}: ${message}`);
            
            if (status === 401) {
                console.log('💡 可能的原因：');
                console.log('   - 令牌无效或过期');
                console.log('   - 令牌未正确设置');
            } else if (status === 403) {
                console.log('💡 可能的原因：');
                console.log('   - 集成缺少必要权限');
                console.log('   - 没有页面连接到集成');
            }
        } else {
            console.error('网络错误:', error.message);
        }
        
        process.exit(1);
    }
}

// 运行脚本
getPageIds().catch(console.error);
